|-main.py
|
|-app    #存放接口
| |-routers
|   |-auth.py  #用户认证相关
|   |  |-POST /api/register  注册账号
|   |  |-POST /api/login  用户登录
|   |  |-PUT /api/activate  激活账号
|   |  |-PUT /api/change-password  修改密码
|   |
|   |-users.py  #用户信息管理
|   |  |-GET /api/user-info  获取用户信息
|   |  |-GET /api/protected-route  受保护的路由测试
|   |
|   |-courses.py  #课程管理
|   |  |-GET /api/course-list  获取课程列表
|   |  |-GET /api/course-info  获取课程信息
|   |  |-GET /api/course-student-list  获取课程学生列表
|   |  |-POST /api/course  创建课程
|   |  |-PUT /api/course  重命名课程
|   |  |-DELETE /api/course  删除课程
|   |
|   |-modules.py  #模块管理
|   |  |-GET /api/module-list  获取module列表
|   |  |-GET /api/module-chatbot-list  获取module关联的chatbot列表
|   |  |-POST /api/module  创建module
|   |  |-PUT /api/module  重命名module 
|   |  |-DELETE /api/module  删除module
|   |  |-POST /api/module-chatbots  添加module中的chatbot
|   |  |-DELETE /api/module-chatbot  删除module中的chatbot
|   |
|   |-chatbots.py  #聊天机器人管理
|   |  |-GET /api/chatbot-list  在left side bar的chatbot选项中获取chatbot列表(teacher),学生没有这个选项
|   |  |-GET /api/chatbot  获取chatbot信息
|   |  |-POST /api/chatbot  创建chatbot
|   |  |-PUT /api/chatbot  更新chatbot
|   |  |-DELETE /api/chatbot  删除chatbot
|   |  |-GET /api/chatbot-list-by-module  根据module获取chatbot列表（？？？）
|   |  |-GET /api/chatbot-session-list-by-module  根据module获取chatbot会话列表（？？？）
|   |  |-GET /api/chatbot-session-list-by-module-and-chatbot  根据module和chatbot获取会话列表（？？？）
|   |  |-GET /api/chatbot-usage-session-list  查看用户chatbot session的usage
|   |  |-GET /api/chatbot-usage-session-list-for-all-users  查看所有用户chatbot session的usage
|   |  |-GET /api/modules-chatbots  获取module关联的chatbot(?)
|   |
|   |-chat_sessions.py  #聊天会话管理
|   |  |-GET /api/chat-session  获取个人聊天会话会话记录
|   |  |-GET /api/chat-session-by-student  获取学生chatbot聊天会话记录
|   |  |-POST /api/chat-session  创建聊天session
|   |  |-DELETE /api/chat-session  删除聊天secsion
|   |  |-PUT /api/chat-session-name  更新会话名称
|   |  |-PUT /api/chat-session-conversation  更新会话对话内容
|   |  |-GET /api/chat-session-sharing  获取上传的会话信息
|   |  |-POST /api/chat-session-sharing  上传会话用于分享
|   |  |-POST /api/chat-session-attach-file  上传文件到会话
|   |  |-POST /api/chat-session-ocr-image  上传图片进行OCR识别
|   |  |-POST /api/chat-session-web-search  网络搜索
|   |  |-POST /api/chat-session-checklist-progress  获取检查清单进度
|   |  |-POST /api/chat-session-summary  获取会话摘要
|   |  |-GET /api/chat-session-description  获取会话描述
|   |  |-PUT /api/chat-session-description  更新会话描述
|   |
|   |-messages.py  #消息处理
|   |  |-POST /api/messages  处理新消息
|   |  |-POST /api/messages-stream  流式消息处理
|   |  |-POST /api/messages-stream/cancel/{session_id}  取消流式消息
|   |  |-GET /api/messages-stream/active  获取正在进行流式会话的用户列表
|   |
|   |-notifications.py  #通知管理
|   |  |-GET /api/notification-list  获取通知列表
|   |  |-POST /api/notification  创建通知
|   |  |-DELETE /api/notification  删除通知
|   |
|   |-internal_messages.py  #内部消息管理(应该还没加到前端)
|   |  |-GET /api/internal-messages  获取内部消息
|   |  |-POST /api/internal-messages  发送内部消息
|   |  |-PUT /api/internal-messages/mark-read  标记消息为已读
|   |  |-PUT /api/internal-messages/mark-sent-to-chatbot  标记消息已发送给聊天机器人
|   |  |-POST /api/internal-message-notification  创建举手通知
|   |  |-POST /api/internal-messages/raise-hand-notification  创建举手通知（旧版）
|   |  |-GET /api/chat-session-info  获取聊天会话信息
|   |  |-GET /api/course-teachers  获取课程教师
|   |  |-GET /api/internal-messages/raise-hand-notifications  获取举手通知
|   |  |-DELETE /api/internal-messages/raise-hand-notification/{notification_id}  删除举手通知
|   |  |-GET /api/teacher-reply-notifications  获取教师回复通知
|   |  |-GET /api/internal-messages/unsent-to-chatbot  获取未发送给聊天机器人的消息
|   |  |-PUT /api/internal-messages/mark-all-sent-to-chatbot  标记所有消息已发送给聊天机器人
|   |
|   |-groups.py  #群组管理
|   |  |-GET /api/joined-student-group-list  获取已加入group的学生列表
|   |  |-PUT /api/group-course  更新群组课程,将两个课程的group_id改为同一个
|   |  |-POST /api/group  创建群组
|   |  |-GET /api/group  获取群组信息
|   |  |-GET /api/group-by-course  根据课程获取学生群组
|   |
|   |-enrollments.py  #注册管理
|   |  |-POST /api/email-enrollment  通过输入学生email将学生加入课程
|   |  |-DELETE /api/enrollment  删除课程中的学生
|   |
|   |-video.py  #视频处理
|   |  |-POST /api/generate-video  生成视频
|   |  |-POST /api/update-script  更新脚本
|   |  |-GET /api/get-script/{session_id}  获取脚本
|   |  |-POST /api/generate-script  生成脚本
|   |
|   |-avatars.py  #头像管理
|   |  |-DELETE /api/delete_avatar  删除头像
|   |  |-GET /api/get_avatar_knowledge/{avatar_id}  获取头像知识
|   |  |-DELETE /api/delete-module-avatar  删除模块头像
|   |  |-GET /api/module-avatar-list  获取模块头像列表
|   |  |-POST /api/module-avatars  添加模块头像
|   |  |-GET /api/avatar-list-by-module  根据模块获取头像列表
|   |  |-POST /api/create_new_avatar  创建新头像
|   |  |-GET /api/get_avatar_list  获取头像列表
|   |  |-PUT /api/update_avatar/{avatar_id}  更新头像
|
|-utils  #工具函数库
| |-chat  #聊天API集成
| | |-chat_azure_openai.py  #Azure OpenAI API集成
| | |  |-chat_by_azure_openai_api()  使用Azure OpenAI API生成聊天回复
| | |  |-parse_azure_openai_api_res()  解析Azure OpenAI API响应并格式化为数据库记录
| | |
| | |-chat_hkbu_chatgpt.py  #HKBU ChatGPT API集成
| | |  |-chat_by_hkbu_chatgpt_api()  使用HKBU ChatGPT API生成聊天回复
| | |  |-parse_hkbu_chatgpt_api_res()  解析HKBU ChatGPT API响应并格式化为数据库记录
| | |  |-call_hkbu_chatgpt_api()  调用HKBU ChatGPT API的底层函数
| | |
| | |-chat_openrouter.py  #OpenRouter API集成
| | |  |-chat_by_openrouter_api()  使用OpenRouter API生成聊天回复
| | |  |-chat_by_openrouter_api_streaming()  使用OpenRouter API生成流式聊天回复
| | |  |-parse_openrouter_api_res()  解析OpenRouter API响应并格式化为数据库记录
| | |  |-call_openrouter_api()  调用OpenRouter API的底层函数
| | |  |-call_openrouter_api_streaming()  调用OpenRouter API流式响应的底层函数
| | |
| | |-chat_qwen.py  #通义千问API集成
| | |  |-chat_by_qwen_api()  使用通义千问API生成聊天回复
| | |  |-parse_qwen_api_res()  解析通义千问API响应并格式化为数据库记录
| | 
| |-doc  #文档处理
| | |-doc_azure.py  #Azure文档智能服务集成
| | |  |-analyze_document()  使用Azure Document Intelligence分析文档内容
| |
| |-image  #图像生成
| | |-image_azure_dalle.py  #Azure DALL-E图像生成集成
| | |  |-image_by_azure_dalle()  使用Azure DALL-E生成图像
| | |  |-parse_azure_dalle_api_res()  解析Azure DALL-E API响应并格式化为数据库记录
| |
| |-prompts  #提示词模板
|   |-prompt_get_checklist_progress.py  #检查清单进度提示词
|   |  |-get_prompt_checklist_progress()  获取检查清单进度分析的提示词模板
|   |
|   |-prompt_web_search.py  #网络搜索提示词
|   |  |-get_prompt_web_search()  获取网络搜索功能的提示词模板
